# 知识图谱构建系统 - 技术架构图

## 1. 整体系统架构

```mermaid
graph TB
    subgraph "用户接口层"
        A1[Web界面<br/>Streamlit]
        A2[API接口<br/>FastAPI]
        A3[CLI工具<br/>Click]
    end
    
    subgraph "应用服务层"
        B1[知识图谱构建服务]
        B2[GraphRAG查询服务]
        B3[文档处理服务]
        B4[用户管理服务]
    end
    
    subgraph "框架集成层"
        C1[LangGraph<br/>智能体编排]
        C2[LangChain<br/>工具集成]
        C3[OpenAI SDK<br/>模型调用]
    end
    
    subgraph "核心业务层"
        D1[本体论管理器<br/>OntologyManager]
        D2[多模态处理器<br/>MultiModalProcessor]
        D3[知识提取器<br/>KnowledgeExtractor]
        D4[图模型管理器<br/>Neo4jGraphModel]
        D5[社区检测器<br/>CommunityDetector]
        D6[向量存储器<br/>VectorStore]
    end
    
    subgraph "数据存储层"
        E1[(Neo4j<br/>图数据库)]
        E2[(Redis<br/>缓存)]
        E3[(Faiss<br/>向量库)]
        E4[(MinIO<br/>文件存储)]
    end
    
    subgraph "基础设施层"
        F1[vLLM<br/>推理引擎]
        F2[Docker<br/>容器化]
        F3[Kubernetes<br/>编排]
        F4[Prometheus<br/>监控]
    end
    
    A1 --> B1
    A2 --> B2
    A3 --> B3
    
    B1 --> C1
    B2 --> C2
    B3 --> C3
    
    C1 --> D1
    C2 --> D2
    C3 --> D3
    
    D1 --> E1
    D2 --> E2
    D3 --> E3
    D4 --> E4
    
    D5 --> F1
    D6 --> F2
    
    F2 --> F3
    F3 --> F4
```

## 2. 知识图谱构建流程

```mermaid
flowchart TD
    A[文档上传] --> B[文档解析]
    B --> C{文档类型}
    
    C -->|PDF| D1[MinerU解析器]
    C -->|Word| D2[python-docx解析器]
    C -->|图片| D3[OCR解析器]
    C -->|表格| D4[表格解析器]
    
    D1 --> E[文本分块]
    D2 --> E
    D3 --> E
    D4 --> E
    
    E --> F[实体提取]
    F --> G[关系提取]
    G --> H[知识验证]
    H --> I[图谱构建]
    
    I --> J[(Neo4j存储)]
    
    subgraph "LLM处理"
        F --> F1[实体识别LLM]
        G --> G1[关系抽取LLM]
        H --> H1[知识验证LLM]
    end
    
    subgraph "开源工具"
        D1 --> D1A[MinerU 2.0]
        D2 --> D2A[python-docx]
        D3 --> D3A[pytesseract]
        E --> EA[LangChain TextSplitter]
    end
```

## 3. GraphRAG查询架构

```mermaid
graph TB
    A[用户查询] --> B[查询理解]
    B --> C{查询类型}
    
    C -->|实体查询| D1[局部RAG]
    C -->|概念查询| D2[全局RAG]
    C -->|复杂查询| D3[混合RAG]
    
    subgraph "局部RAG流程"
        D1 --> E1[实体识别]
        E1 --> E2[图遍历检索]
        E2 --> E3[子图构建]
        E3 --> E4[上下文生成]
    end
    
    subgraph "全局RAG流程"
        D2 --> F1[向量检索]
        F1 --> F2[社区匹配]
        F2 --> F3[摘要聚合]
        F3 --> F4[上下文生成]
    end
    
    subgraph "混合RAG流程"
        D3 --> G1[双路检索]
        G1 --> G2[结果融合]
        G2 --> G3[重排序]
        G3 --> G4[上下文生成]
    end
    
    E4 --> H[答案生成]
    F4 --> H
    G4 --> H
    
    H --> I[结果返回]
    
    subgraph "数据源"
        J[(Neo4j图数据库)]
        K[(Faiss向量库)]
        L[社区摘要]
    end
    
    E2 --> J
    F1 --> K
    F2 --> L
    G1 --> J
    G1 --> K
```

## 4. 社区检测流程

```mermaid
flowchart TD
    A[(Neo4j图数据)] --> B[图数据导出]
    B --> C[NetworkX转换]
    C --> D{选择算法}
    
    D -->|高质量| E1[Leiden算法]
    D -->|经典| E2[Louvain算法]
    D -->|快速| E3[贪心算法]
    
    E1 --> F[社区划分结果]
    E2 --> F
    E3 --> F
    
    F --> G[社区质量评估]
    G --> H{质量满足?}
    
    H -->|否| I[参数调优]
    I --> D
    
    H -->|是| J[社区摘要生成]
    J --> K[LLM摘要生成]
    K --> L[(向量存储)]
    
    subgraph "开源算法库"
        E1 --> E1A[leidenalg]
        E2 --> E2A[python-louvain]
        E3 --> E3A[NetworkX]
    end
    
    subgraph "质量指标"
        G --> G1[模块度]
        G --> G2[轮廓系数]
        G --> G3[社区内聚度]
    end
```

## 5. 多模态文档处理架构

```mermaid
graph TB
    A[文档输入] --> B[文件类型检测]
    B --> C{文档类型}
    
    C -->|PDF| D1[PDF处理流水线]
    C -->|Word| D2[Word处理流水线]
    C -->|图片| D3[图片处理流水线]
    C -->|表格| D4[表格处理流水线]
    
    subgraph "PDF处理"
        D1 --> D1A[MinerU高保真解析]
        D1A --> D1B[文本提取]
        D1A --> D1C[图片提取]
        D1A --> D1D[表格提取]
        D1A --> D1E[公式提取]
    end
    
    subgraph "Word处理"
        D2 --> D2A[python-docx解析]
        D2A --> D2B[文本提取]
        D2A --> D2C[图片提取]
        D2A --> D2D[表格提取]
    end
    
    subgraph "图片处理"
        D3 --> D3A[Pillow图像处理]
        D3A --> D3B[OCR文字识别]
        D3B --> D3C[pytesseract]
    end
    
    subgraph "表格处理"
        D4 --> D4A[pandas表格解析]
        D4A --> D4B[结构化数据提取]
    end
    
    D1B --> E[内容整合]
    D1C --> E
    D1D --> E
    D1E --> E
    D2B --> E
    D2C --> E
    D2D --> E
    D3C --> E
    D4B --> E
    
    E --> F[LangChain文本分块]
    F --> G[多模态文档对象]
```

## 6. LLM客户端架构

```mermaid
classDiagram
    class BaseLLMClient {
        <<abstract>>
        +generate(messages) str
        +embed(texts) List[float]
        +stream_generate(messages) Iterator
    }
    
    class OpenAICompatibleClient {
        -client: OpenAI
        -model: str
        -base_url: str
        +generate(messages) str
        +embed(texts) List[float]
    }
    
    class VLLMClient {
        -host: str
        -port: int
        -model: str
        +generate(messages) str
    }
    
    class OllamaClient {
        -host: str
        -port: int
        -model: str
        +generate(messages) str
    }
    
    class ThirdPartyClient {
        -provider: str
        -api_key: str
        +generate(messages) str
    }
    
    class LLMClientFactory {
        +create_client(config) BaseLLMClient
    }
    
    BaseLLMClient <|-- OpenAICompatibleClient
    OpenAICompatibleClient <|-- VLLMClient
    OpenAICompatibleClient <|-- OllamaClient
    OpenAICompatibleClient <|-- ThirdPartyClient
    
    LLMClientFactory --> BaseLLMClient : creates
```

## 7. 数据流架构

```mermaid
sequenceDiagram
    participant U as 用户
    participant API as FastAPI
    participant KG as 知识图谱服务
    participant DOC as 文档处理器
    participant LLM as LLM客户端
    participant NEO as Neo4j
    participant VEC as 向量存储
    
    U->>API: 上传文档
    API->>KG: 构建知识图谱请求
    KG->>DOC: 解析文档
    DOC->>DOC: MinerU/PyMuPDF解析
    DOC->>KG: 返回解析结果
    
    KG->>LLM: 实体提取请求
    LLM->>LLM: vLLM/Ollama推理
    LLM->>KG: 返回实体列表
    
    KG->>LLM: 关系提取请求
    LLM->>KG: 返回关系列表
    
    KG->>NEO: 存储图数据
    KG->>VEC: 存储向量数据
    
    NEO->>KG: 确认存储
    VEC->>KG: 确认存储
    
    KG->>API: 返回构建结果
    API->>U: 返回成功响应
```

## 8. 部署架构

```mermaid
graph TB
    subgraph "Kubernetes集群"
        subgraph "应用层Pod"
            A1[kg-api-pod-1]
            A2[kg-api-pod-2]
            A3[kg-web-pod-1]
        end
        
        subgraph "服务层Pod"
            B1[vllm-pod-1]
            B2[vllm-pod-2]
        end
        
        subgraph "数据层Pod"
            C1[neo4j-pod-1]
            C2[redis-pod-1]
            C3[minio-pod-1]
        end
        
        subgraph "监控层Pod"
            D1[prometheus-pod]
            D2[grafana-pod]
        end
    end
    
    subgraph "负载均衡"
        E1[Ingress Controller]
        E2[Service Mesh]
    end
    
    subgraph "存储"
        F1[PVC - Neo4j Data]
        F2[PVC - Redis Data]
        F3[PVC - MinIO Data]
    end
    
    E1 --> A1
    E1 --> A2
    E1 --> A3
    
    A1 --> B1
    A2 --> B2
    A3 --> B1
    
    A1 --> C1
    A2 --> C1
    A3 --> C2
    
    C1 --> F1
    C2 --> F2
    C3 --> F3
    
    D1 --> A1
    D1 --> A2
    D1 --> A3
    D1 --> B1
    D1 --> B2
    D1 --> C1
    D1 --> C2
    D1 --> C3
```

## 9. 开源项目依赖关系

```mermaid
mindmap
  root((知识图谱系统))
    AI/ML框架
      LangChain
        文档加载器
        文本分块器
        向量存储
        工具集成
      LangGraph
        智能体编排
        状态管理
        工作流定义
      Transformers
        模型加载
        Tokenizer
        预训练模型
      sentence-transformers
        语义嵌入
        相似度计算
    
    图数据库
      Neo4j
        图存储
        Cypher查询
        图算法库
      NetworkX
        图分析
        算法实现
        数据转换
    
    文档处理
      MinerU
        高保真解析
        多模态支持
      PyMuPDF
        PDF处理
        文本提取
      python-docx
        Word处理
        结构提取
      Pillow
        图像处理
        格式转换
      pytesseract
        OCR识别
        文字提取
    
    向量搜索
      Faiss
        向量索引
        相似度搜索
        GPU加速
      ChromaDB
        向量数据库
        元数据存储
        查询接口
    
    Web框架
      FastAPI
        API服务
        异步支持
        自动文档
      Streamlit
        Web界面
        数据可视化
        交互组件
    
    部署工具
      Docker
        容器化
        镜像构建
      Kubernetes
        容器编排
        服务发现
        负载均衡
      Helm
        包管理
        配置管理
```

## 10. 监控架构

```mermaid
graph TB
    subgraph "应用层监控"
        A1[FastAPI Metrics]
        A2[Streamlit Metrics]
        A3[Custom Business Metrics]
    end
    
    subgraph "服务层监控"
        B1[vLLM Metrics]
        B2[Neo4j Metrics]
        B3[Redis Metrics]
    end
    
    subgraph "基础设施监控"
        C1[Node Exporter]
        C2[cAdvisor]
        C3[Kubernetes Metrics]
    end
    
    subgraph "日志收集"
        D1[Fluentd]
        D2[Elasticsearch]
        D3[Kibana]
    end
    
    subgraph "监控存储"
        E1[(Prometheus)]
        E2[(InfluxDB)]
    end
    
    subgraph "可视化告警"
        F1[Grafana]
        F2[AlertManager]
    end
    
    A1 --> E1
    A2 --> E1
    A3 --> E1
    B1 --> E1
    B2 --> E1
    B3 --> E1
    C1 --> E1
    C2 --> E1
    C3 --> E1
    
    A1 --> D1
    A2 --> D1
    B1 --> D1
    D1 --> D2
    D2 --> D3
    
    E1 --> F1
    E2 --> F1
    E1 --> F2
    
    F2 --> G[钉钉/邮件/短信]
```

这些架构图可以直接在支持Mermaid的工具中渲染，如：
- GitHub/GitLab Markdown
- Mermaid Live Editor
- VS Code Mermaid插件
- 各种文档工具

在PPT制作时，可以将这些图表导出为PNG/SVG格式插入到演示文稿中。
